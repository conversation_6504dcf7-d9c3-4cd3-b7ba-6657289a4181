package api.service

import api.ApiClient
import api.dto.*
import data.Sale

/**
 * Service for sales management operations with the backend API
 */
class SalesService(private val apiClient: ApiClient) {

    /**
     * Get all sales with pagination support
     */
    suspend fun getSales(page: Int = 0, size: Int = 10): PageResponse<SaleDto> {
        return apiClient.get("/sales?page=$page&size=$size")
    }

    /**
     * Get a sale by ID
     */
    suspend fun getSaleById(id: Long): SaleDto {
        return apiClient.get("/sales/$id")
    }

    /**
     * Create a new sale
     */
    suspend fun createSale(saleRequest: SaleCreateRequest): SaleDto {
        return apiClient.post("/sales", saleRequest)
    }

    /**
     * Get today's sales
     */
    suspend fun getTodaySales(): List<SaleDto> {
        return apiClient.get("/sales/today")
    }

    /**
     * Get sales by date range
     */
    suspend fun getSalesByDateRange(startDate: String, endDate: String): List<SaleDto> {
        return apiClient.get("/sales/period?startDate=$startDate&endDate=$endDate")
    }

    /**
     * Create sale from domain model
     */
    suspend fun createSaleFromDomain(sale: Sale): SaleDto {
        val request = sale.toSaleCreateRequest()
        return createSale(request)
    }

    /**
     * Get sale receipt
     */
    suspend fun getSaleReceipt(id: Long): String {
        return apiClient.get("/sales/$id/receipt")
    }
}
