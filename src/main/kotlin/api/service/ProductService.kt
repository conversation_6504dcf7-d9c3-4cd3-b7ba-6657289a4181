package api.service

import api.ApiClient
import api.dto.ProductDto
import api.dto.PageResponse

/**
 * Service for product management operations with the backend API
 */
class ProductService(private val apiClient: ApiClient) {

    /**
     * Get all products with pagination support
     */
    suspend fun getProducts(
        page: Int = 0,
        size: Int = 10,
        search: String? = null,
        category: String? = null,
        sortBy: String? = null,
        sortDir: String? = null
    ): PageResponse<ProductDto> {
        val params = buildString {
            append("?page=$page&size=$size")
            if (!search.isNullOrBlank()) append("&search=$search")
            if (!category.isNullOrBlank()) append("&category=$category")
            if (!sortBy.isNullOrBlank()) append("&sortBy=$sortBy")
            if (!sortDir.isNullOrBlank()) append("&sortDir=$sortDir")
        }
        return apiClient.get("/products$params")
    }

    /**
     * Get a product by ID
     */
    suspend fun getProductById(id: Long): ProductDto {
        return apiClient.get("/products/$id")
    }

    /**
     * Search products by query
     */
    suspend fun searchProducts(query: String): List<ProductDto> {
        return apiClient.get("/products/search?q=$query")
    }

    /**
     * Create a new product
     */
    suspend fun createProduct(product: ProductDto): ProductDto {
        return apiClient.post("/products", product)
    }

    /**
     * Update an existing product
     */
    suspend fun updateProduct(id: Long, product: ProductDto): ProductDto {
        return apiClient.put("/products/$id", product)
    }

    /**
     * Delete a product
     */
    suspend fun deleteProduct(id: Long) {
        apiClient.delete("/products/$id")
    }

    /**
     * Update product stock directly
     */
    suspend fun updateStock(id: Long, stockQuantity: Int): ProductDto {
        return apiClient.put("/products/$id/stock", mapOf("stockQuantity" to stockQuantity))
    }

    /**
     * Increase product stock
     */
    suspend fun increaseStock(id: Long, quantity: Int): ProductDto {
        return apiClient.post("/products/$id/stock/increase", mapOf("quantity" to quantity))
    }

    /**
     * Reduce product stock
     */
    suspend fun reduceStock(id: Long, quantity: Int): ProductDto {
        return apiClient.post("/products/$id/stock/reduce", mapOf("quantity" to quantity))
    }

    /**
     * Get products by category
     */
    suspend fun getProductsByCategory(category: String, page: Int = 0, size: Int = 10): PageResponse<ProductDto> {
        return apiClient.get("/products?category=$category&page=$page&size=$size")
    }

    /**
     * Get low stock products
     */
    suspend fun getLowStockProducts(): List<ProductDto> {
        return apiClient.get("/products/low-stock")
    }
}