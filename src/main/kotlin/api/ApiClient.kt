package api

import io.ktor.client.*
import io.ktor.client.call.*
import io.ktor.client.engine.cio.*
import io.ktor.client.plugins.auth.*
import io.ktor.client.plugins.auth.providers.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.client.plugins.logging.*
import io.ktor.client.request.*
import io.ktor.client.statement.*
import io.ktor.http.*
import io.ktor.serialization.kotlinx.json.*
import kotlinx.serialization.json.Json

/**
 * ApiClient provides methods for making HTTP requests to the backend API
 */
class ApiClient(
    private val baseUrl: String = "http://localhost:8080/api"
) {
    private var authToken: String? = null

    // Initialize HTTP client with plugins
    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                prettyPrint = true
                isLenient = true
                ignoreUnknownKeys = true
            })
        }

        install(Auth) {
            bearer {
                loadTokens {
                    authToken?.let {
                        BearerTokens(it, it)
                    }
                }
            }
        }

        install(Logging) {
            logger = Logger.DEFAULT
            level = LogLevel.HEADERS
        }
    }

    /**
     * Set the authentication token for subsequent requests
     */
    fun setAuthToken(token: String) {
        authToken = token
    }

    /**
     * Clear the authentication token
     */
    fun clearAuthToken() {
        authToken = null
    }

    /**
     * Perform a GET request and parse the response to the specified type
     */
    suspend inline fun <reified T> get(endpoint: String): T {
        val response: HttpResponse = client.get("$baseUrl$endpoint")
        return response.body()
    }

    /**
     * Perform a POST request with a request body and parse the response to the specified type
     */
    suspend inline fun <reified T, reified R> post(endpoint: String, body: T): R {
        val response: HttpResponse = client.post("$baseUrl$endpoint") {
            contentType(ContentType.Application.Json)
            setBody(body)
        }
        return response.body()
    }

    /**
     * Perform a PUT request with a request body and parse the response to the specified type
     */
    suspend inline fun <reified T, reified R> put(endpoint: String, body: T): R {
        val response: HttpResponse = client.put("$baseUrl$endpoint") {
            contentType(ContentType.Application.Json)
            setBody(body)
        }
        return response.body()
    }

    /**
     * Perform a DELETE request and return the response
     */
    suspend fun delete(endpoint: String): HttpResponse {
        return client.delete("$baseUrl$endpoint")
    }
}
