package data

import api.ApiClient
import api.dto.toCustomer
import api.dto.toProduct
import api.dto.toSale
import api.dto.toSaleCreateRequest
import api.service.AuthService
import api.service.CustomerService
import api.service.ProductService
import api.service.SalesService
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.todayIn
import kotlinx.datetime.toLocalDateTime
import kotlin.random.Random

class SalesDataManager {
    // API Client and Services
    private val apiClient = ApiClient()
    private val authService = AuthService(apiClient)
    private val productService = ProductService(apiClient)
    private val customerService = CustomerService(apiClient)
    private val salesService = SalesService(apiClient)

    // Cached data for offline use
    private var _products = mutableListOf<Product>()
    private var _customers = mutableListOf<Customer>()
    private var _sales = mutableListOf<Sale>()

    // Flag to determine if we're using API or local data
    private var useApiData = true

    init {
        // Initialize with local data as fallback
        initializeLocalData()

        // Try to fetch data from API
        tryFetchInitialData()
    }

    private fun initializeLocalData() {
        // بيانات تجريبية للمنتجات
        _products = mutableListOf(
            Product(1, "iPhone 14", "123456789012", 4500.0, 4000.0, 15, "إلكترونيات"),
            Product(2, "Samsung Galaxy S23", "123456789013", 4200.0, 3700.0, 12, "إلكترونيات"),
            Product(3, "لابتوب HP", "123456789014", 3500.0, 3000.0, 8, "إلكترونيات"),
            Product(4, "سماعات AirPods", "123456789015", 750.0, 600.0, 25, "إكسسوارات"),
            Product(5, "شاحن سريع", "123456789016", 150.0, 100.0, 50, "إكسسوارات"),
            Product(6, "كيبورد لاسلكي", "123456789017", 200.0, 150.0, 30, "إكسسوارات"),
            Product(7, "ماوس جيمنج", "123456789018", 180.0, 130.0, 20, "إكسسوارات"),
            Product(8, "شاشة 27 بوصة", "123456789019", 1200.0, 1000.0, 10, "إلكترونيات")
        )

        // بيانات تجريبية للعملاء
        _customers = mutableListOf(
            Customer(1, "أحمد محمد", "0501234567", "<EMAIL>", "الرياض", 5500.0),
            Customer(2, "فاطمة علي", "0507654321", "<EMAIL>", "جدة", 3200.0),
            Customer(3, "محمد السعيد", "0509876543", "<EMAIL>", "الدمام", 2800.0),
            Customer(4, "نورا أحمد", "0502468135", "<EMAIL>", "مكة", 4100.0),
            Customer(5, "خالد العتيبي", "0508642097", "<EMAIL>", "الرياض", 6700.0)
        )

        // Generate sample sales
        generateSampleSales()
    }

    private fun tryFetchInitialData() {
        try {
            runBlocking {
                // Try to fetch products to test API connectivity
                val productsResponse = productService.getProducts(page = 0, size = 50)
                if (productsResponse.content.isNotEmpty()) {
                    _products = productsResponse.content.map { it.toProduct() }.toMutableList()
                    useApiData = true

                    // If connected, fetch customers too
                    val customersResponse = customerService.getCustomers(page = 0, size = 50)
                    _customers = customersResponse.content.map { it.toCustomer() }.toMutableList()

                    // Get today's sales
                    val todaySales = salesService.getTodaySales()
                    _sales = todaySales.map { it.toSale() }.toMutableList()
                }
            }
        } catch (e: Exception) {
            println("Failed to connect to API: ${e.message}")
            println("Using local data instead")
            useApiData = false
        }
    }

    private fun generateSampleSales() {
        _sales = mutableListOf()
        repeat(20) { index ->
            val customer = if (Random.nextBoolean()) _customers.random() else null
            val itemCount = Random.nextInt(1, 4)
            val items = mutableListOf<SaleItem>()

            repeat(itemCount) {
                val product = _products.random()
                val quantity = Random.nextInt(1, 5)
                items.add(SaleItem(product, quantity, product.price))
            }

            _sales.add(
                Sale(
                    id = index + 1,
                    date = Clock.System.now().minus(kotlin.time.Duration.parse("${Random.nextInt(0, 30)}d")).toLocalDateTime(TimeZone.currentSystemDefault()),
                    customer = customer,
                    items = items,
                    tax = items.sumOf { it.subtotal } * 0.15,
                    paymentMethod = PaymentMethod.values().random()
                )
            )
        }
    }

    // Public properties using API data when available, falling back to local data
    val products: List<Product> get() = _products
    val customers: List<Customer> get() = _customers
    val sales: List<Sale> get() = _sales

    // حساب الإحصائيات اليومية
    fun getDailySalesStats(date: LocalDate): DailySalesStats {
        val todaySales = _sales.filter {
            it.date.date == date
        }

        val totalSales = todaySales.sumOf { it.total }
        val totalTransactions = todaySales.size
        val allItems = todaySales.flatMap { it.items }
        val topProduct = allItems.groupBy { it.product }
            .maxByOrNull { entry -> entry.value.sumOf { item -> item.quantity } }?.key
        val totalProfit = allItems.sumOf { (it.product.price - it.product.cost) * it.quantity }
        val totalItemsSold = allItems.sumOf { it.quantity }
        val averageOrderValue = if (totalTransactions > 0) totalSales / totalTransactions else 0.0

        return DailySalesStats(date, totalSales, totalTransactions, topProduct, totalProfit, averageOrderValue, totalItemsSold)
    }

    // حساب إحصائيات المنتجات
    fun getProductStats(): List<ProductStats> {
        return _products.map { product ->
            val soldItems = _sales.flatMap { it.items }.filter { item -> item.product.id == product.id }
            val totalSold = soldItems.sumOf { item -> item.quantity }
            val revenue = soldItems.sumOf { item -> item.subtotal }
            val profit = soldItems.sumOf { item -> (product.price - product.cost) * item.quantity }

            ProductStats(product, totalSold, revenue, profit)
        }.sortedByDescending { it.revenue }
    }

    // البحث عن المنتجات
    fun searchProducts(query: String): List<Product> {
        if (useApiData) {
            return try {
                runBlocking {
                    val response = productService.getProducts(search = query)
                    response.content.map { it.toProduct() }
                }
            } catch (e: Exception) {
                // Fall back to local search
                searchProductsLocally(query)
            }
        } else {
            return searchProductsLocally(query)
        }
    }

    private fun searchProductsLocally(query: String): List<Product> {
        return _products.filter {
            it.name.contains(query, ignoreCase = true) ||
            it.barcode.contains(query) ||
            it.category.contains(query, ignoreCase = true)
        }
    }

    // Save a new sale (create in API if connected)
    suspend fun saveSale(sale: Sale): Sale? {
        if (useApiData) {
            try {
                // Send to API
                val savedSale = salesService.createSaleFromDomain(sale)
                val domainSale = savedSale.toSale()

                // Update local cache
                _sales.add(domainSale)
                return domainSale
            } catch (e: Exception) {
                println("Failed to save sale to API: ${e.message}")
                // Fall back to local save
                return saveLocalSale(sale)
            }
        } else {
            return saveLocalSale(sale)
        }
    }

    private fun saveLocalSale(sale: Sale): Sale {
        // Generate local ID
        val newId = if (_sales.isEmpty()) 1 else _sales.maxOf { it.id } + 1
        val newSale = sale.copy(id = newId)

        // Add to local list
        _sales.add(newSale)
        return newSale
    }

    // Add or update customer
    suspend fun saveCustomer(customer: Customer): Customer? {
        if (useApiData) {
            try {
                val customerDto = api.dto.CustomerDto(
                    id = customer.id.toLong().takeIf { it > 0 } ?: 0L,
                    name = customer.name,
                    phone = customer.phone,
                    email = customer.email,
                    address = customer.address,
                    totalPurchases = customer.totalPurchases
                )

                val result = if (customer.id <= 0) {
                    // Create new
                    customerService.createCustomer(customerDto)
                } else {
                    // Update existing
                    customerService.updateCustomer(customer.id.toLong(), customerDto)
                }

                // Update local cache
                val savedCustomer = result.toCustomer()
                val existingIndex = _customers.indexOfFirst { it.id == savedCustomer.id }
                if (existingIndex >= 0) {
                    _customers[existingIndex] = savedCustomer
                } else {
                    _customers.add(savedCustomer)
                }

                return savedCustomer
            } catch (e: Exception) {
                println("Failed to save customer to API: ${e.message}")
                return saveLocalCustomer(customer)
            }
        } else {
            return saveLocalCustomer(customer)
        }
    }

    private fun saveLocalCustomer(customer: Customer): Customer {
        if (customer.id <= 0) {
            // New customer
            val newId = if (_customers.isEmpty()) 1 else _customers.maxOf { it.id } + 1
            val newCustomer = customer.copy(id = newId)
            _customers.add(newCustomer)
            return newCustomer
        } else {
            // Update existing
            val existingIndex = _customers.indexOfFirst { it.id == customer.id }
            if (existingIndex >= 0) {
                _customers[existingIndex] = customer
            } else {
                _customers.add(customer)
            }
            return customer
        }
    }

    // Login user and get authentication token
    suspend fun login(username: String, password: String): Boolean {
        return try {
            val response = authService.login(username, password)
            useApiData = true
            true
        } catch (e: Exception) {
            println("Login failed: ${e.message}")
            false
        }
    }

    // Check if we're using API data or local fallback
    fun isUsingApi(): Boolean = useApiData
}
